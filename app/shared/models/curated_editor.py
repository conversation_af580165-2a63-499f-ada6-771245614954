"""
Models for curated content editor operations.

This module contains Pydantic models used for curated content editing,
including request/response models for CRUD operations on curated sets and tasks.
"""

from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List, Union
from datetime import datetime


class CuratedSetResponse(BaseModel):
    """Response model for curated set data."""
    id: str = Field(..., description="Curated set ID")
    title: str = Field(..., description="Set title")
    input_type: str = Field(..., description="Input type (text, audio, etc.)")
    theme_id: Optional[str] = Field(None, description="Associated theme ID")
    tasks: List[str] = Field(..., description="List of task IDs")
    stories: List[str] = Field(..., description="List of story IDs")
    total_tasks: int = Field(..., description="Total number of tasks")
    total_stories: int = Field(..., description="Total number of stories")
    text_tasks_ready: int = Field(..., description="Number of text tasks ready")
    media_tasks_pending: int = Field(..., description="Number of media tasks pending")
    status: str = Field(..., description="Set status")
    gentype: str = Field(..., description="Generation type")
    has_follow_up: bool = Field(..., description="Whether set has follow-up content")
    difficulty_level: Optional[int] = Field(None, description="Difficulty level (1-3)")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    
    class Config:
        from_attributes = True


class CuratedSetUpdate(BaseModel):
    """Model for updating curated set properties."""
    title: Optional[str] = Field(None, description="Set title")
    status: Optional[str] = Field(None, description="Set status")
    difficulty_level: Optional[int] = Field(None, ge=1, le=3, description="Difficulty level (1-3)")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class TaskItemBasic(BaseModel):
    """Basic task item information for listing."""
    id: str = Field(..., description="Task item ID")
    type: str = Field(..., description="Task type")
    title: str = Field(..., description="Task title")
    status: str = Field(..., description="Task status")
    difficulty_level: int = Field(..., description="Difficulty level")
    total_score: int = Field(..., description="Total score for task")
    
    class Config:
        from_attributes = True


class QuestionData(BaseModel):
    """Question data structure."""
    text: str = Field(..., description="Question text in Nepali")
    translated_text: Optional[str] = Field(None, description="Question text in English")
    options:Dict[str, str] = Field(..., description="Answer options in Nepali")
    options_en: Optional[Dict[str, str]] = Field(None, description="Answer options in English")
    correct_answer_index: int = Field(..., description="Index of correct answer")
    audio_metadata: Optional[Dict[str, Any]] = Field(None, description="Audio metadata")
    image_metadata: Optional[Dict[str, Any]] = Field(None, description="Image metadata")
    options_metadata: Optional[Dict[str, Any]] = Field(None, description="Options metadata")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Options metadata")

class CorrectAnswerData(BaseModel):
    """Correct answer data structure."""
    type: str = Field(..., description="Answer type (single_choice, multiple_choice, image_identification)")
    value: Any = Field(..., description="Answer value - string for single/image, list for multiple")


class TaskItemDetailed(BaseModel):
    """Detailed task item with all properties."""
    id: str = Field(..., description="Task item ID")
    type: str = Field(..., description="Task type")
    title: str = Field(..., description="Task title")
    question: QuestionData = Field(..., description="Question data")
    correct_answer: CorrectAnswerData = Field(..., description="Correct answer data")
    status: str = Field(..., description="Task status")
    difficulty_level: int = Field(..., description="Difficulty level")
    total_score: int = Field(..., description="Total score for task")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    
    class Config:
        from_attributes = True


class TaskItemUpdate(BaseModel):
    """Model for updating task item properties."""
    title: Optional[str] = Field(None, description="Task title")
    question: Optional[QuestionData] = Field(None, description="Question data")
    correct_answer: Optional[CorrectAnswerData] = Field(None, description="Correct answer data")
    difficulty_level: Optional[int] = Field(None, ge=1, le=3, description="Difficulty level (1-3)")
    total_score: Optional[int] = Field(None, ge=1, description="Total score for task")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class CuratedSetListResponse(BaseModel):
    """Response model for curated sets list with pagination."""
    sets: List[CuratedSetResponse] = Field(..., description="List of curated sets")
    pagination: Dict[str, Any] = Field(..., description="Pagination metadata")
    filters: Dict[str, Any] = Field(..., description="Applied filters")


class TaskItemListResponse(BaseModel):
    """Response model for task items list."""
    tasks: List[TaskItemBasic] = Field(..., description="List of task items")
    set_info: Dict[str, Any] = Field(..., description="Curated set information")
    total_count: int = Field(..., description="Total number of tasks in set")


class CorrectAnswerEdit(BaseModel):
    """Model for editing correct answer based on task type."""
    type: str = Field(..., description="Answer type (single_choice, multiple_choice, image_identification)")
    value: Any = Field(..., description="Answer value - string for single/image, list for multiple")


class QuestionEditRequest(BaseModel):
    """Model for editing question text, options, and correct answer."""
    text: Optional[str] = Field(None, description="Question text in Nepali")
    translated_text: Optional[str] = Field(None, description="Question text in English")
    options: Optional[Dict[str, str]] = Field(None, description="Answer options in Nepali")
    options_en: Optional[Dict[str, str]] = Field(None, description="Answer options in English")
    answer_hint: Optional[str] = Field(None, description="Answer hint text")
    correct_answer: Optional[CorrectAnswerEdit] = Field(None, description="Correct answer data")
    regenerate_audio: bool = Field(True, description="Whether to regenerate audio for options")


class QuestionEditResponse(BaseModel):
    """Response model for question edit operation."""
    task_id: str = Field(..., description="Task ID that was updated")
    updated_fields: List[str] = Field(..., description="List of fields that were updated")
    audio_regenerated: bool = Field(..., description="Whether audio was regenerated")
    options_metadata: Optional[Dict[str, Any]] = Field(None, description="Updated options metadata")
    correct_answer_updated: bool = Field(..., description="Whether correct answer was updated")
    hint_media_generated: bool = Field(..., description="Whether hint media (image/audio) was generated")
    hint_media_type: Optional[str] = Field(None, description="Type of hint media generated (image/audio)")
    question_metadata: Optional[Dict[str, Any]] = Field(None, description="Updated question metadata")
    message: str = Field(..., description="Success message")
